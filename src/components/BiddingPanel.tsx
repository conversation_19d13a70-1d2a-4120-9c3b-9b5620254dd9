import { useState, useEffect } from "react";
import { LiveReel, Bid } from "@/lib/types";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { useSocketContext } from "@/lib/contexts/SocketContext";
import { Clock, TrendingUp, Users, Gavel, Plus, Wifi, WifiOff, Timer } from "lucide-react";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";
import { timeSyncService } from "@/lib/timeSync";
import useSocket from "@/hooks/useSocket";
import { getFlightRoute, formatFlightRoute, FlightRoute } from "@/lib/flightUtils";

interface BiddingPanelProps {
  reel: LiveReel;
  bids: Bid[];
  onPlaceBid: (amount: number) => void;
  className?: string;
  backendEndTime?: number | null; // Optional backend endTime (Unix timestamp)
}

export function BiddingPanel({
  reel,
  bids,
  onPlaceBid,
  className,
  backendEndTime,
}: BiddingPanelProps) {
  const [bidAmount, setBidAmount] = useState<string>("");
  const [isPlacingBid, setIsPlacingBid] = useState(false);
  const [currentTime, setCurrentTime] = useState(Date.now());
  const [flightRoutes, setFlightRoutes] = useState<Record<string, FlightRoute>>({});
  const { isConnected } = useSocketContext();
  const { timeSyncStatus, isTimeSynchronized } = useSocket();

  // Update timer every second
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentTime(Date.now());
    }, 1000);

    return () => clearInterval(interval);
  }, []);

  // Fetch flight route information for flight numbers in bids
  useEffect(() => {
    const fetchFlightRoutes = async () => {
      const flightNumbers = bids
        .map(bid => bid.bidder.flightNumber)
        .filter((flightNumber): flightNumber is string => !!flightNumber)
        .filter(flightNumber => !flightRoutes[flightNumber]);

      for (const flightNumber of flightNumbers) {
        try {
          const route = await getFlightRoute(flightNumber);
          setFlightRoutes(prev => ({
            ...prev,
            [flightNumber]: route
          }));
        } catch (error) {
          console.error(`Error fetching route for ${flightNumber}:`, error);
        }
      }
    };

    if (bids.length > 0) {
      fetchFlightRoutes();
    }
  }, [bids, flightRoutes]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Component to display flight number with route information
  const FlightNumberDisplay = ({ flightNumber }: { flightNumber: string }) => {
    const route = flightRoutes[flightNumber];

    if (!route) {
      return (
        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full">
          ✈️ {flightNumber}
        </span>
      );
    }

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <span className="text-xs bg-blue-100 text-blue-700 px-2 py-0.5 rounded-full cursor-help hover:bg-blue-200 transition-colors">
              ✈️ {flightNumber}
            </span>
          </TooltipTrigger>
          <TooltipContent>
            <p>{formatFlightRoute(route)}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  };

  const formatTimeRemaining = () => {
    // Use synchronized time if available, otherwise fall back to local time
    const now = timeSyncService.isSynchronized()
      ? timeSyncService.getServerTime()
      : currentTime;

    // Use backend endTime if available, otherwise fall back to frontend endTime
    const endTime = backendEndTime ? backendEndTime : reel.endTime.getTime();
    const diff = endTime - now;

    if (diff <= 0) {
      return "Ended";
    }

    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diff % (1000 * 60)) / 1000);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, "0")}:${seconds.toString().padStart(2, "0")}`;
    }
    return `${minutes}:${seconds.toString().padStart(2, "0")}`;
  };

  const getMinimumBid = () => {
    return reel.product.currentBid + 50;
  };

  const handleQuickBid = (increment: number) => {
    const amount = reel.product.currentBid + increment;
    handlePlaceBid(amount);
  };

  const handlePlaceBid = (amount: number) => {
    if (amount <= reel.product.currentBid) {
      toast({
        title: "Invalid Bid",
        description: `Your bid must be higher than the current bid of ${formatCurrency(reel.product.currentBid)}`,
        variant: "destructive",
      });
      return;
    }

    if (!isConnected) {
      toast({
        title: "Connection Error",
        description: "Unable to place bid. Please check your connection.",
        variant: "destructive",
      });
      return;
    }

    if (isPlacingBid) {
      return; // Prevent double submission
    }

    setIsPlacingBid(true);

    // Call the parent's bid handler (which uses Socket.IO)
    onPlaceBid(amount);
    setBidAmount("");

    // Show optimistic feedback
    toast({
      title: "Bid Submitted!",
      description: `Your bid of ${formatCurrency(amount)} is being processed.`,
    });

    // Reset placing state after a short delay
    setTimeout(() => {
      setIsPlacingBid(false);
    }, 2000);
  };

  const handleCustomBid = () => {
    const amount = parseInt(bidAmount);
    if (isNaN(amount) || amount <= 0) {
      toast({
        title: "Invalid Amount",
        description: "Please enter a valid bid amount.",
        variant: "destructive",
      });
      return;
    }

    if (isPlacingBid) {
      return; // Prevent double submission
    }
    handlePlaceBid(amount);
  };

  return (
    <div className={cn("space-y-6", className)}>
      {/* Current Bid Status */}
      <Card className="border-0 bg-gradient-to-r from-green-50 to-emerald-50">
        <CardContent className="p-6">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <div className="text-sm text-gray-600 mb-1">Current Bid</div>
              <div className="text-3xl font-bold text-green-600">
                {formatCurrency(reel.product.currentBid)}
              </div>
              <div className="text-sm text-gray-500 mt-1">
                Starting at {formatCurrency(reel.product.startingPrice)}
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-gray-600 mb-1">Time Remaining</div>
              <div className="text-2xl font-bold text-red-500">
                {formatTimeRemaining()}
              </div>
              <div className="flex items-center justify-end gap-1 text-sm text-gray-500 mt-1">
                <Users className="w-4 h-4" />
                {reel.viewerCount.toLocaleString()} watching
              </div>
            </div>
          </div>

          <Separator className="my-4" />

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              <span className="text-sm font-medium">
                {reel.bidCount} total bids
              </span>
            </div>
            <Badge variant="secondary" className="bg-green-100 text-green-700">
              {reel.isLive ? "Live Auction" : "Auction Ended"}
            </Badge>
          </div>
        </CardContent>
      </Card>

      {/* Quick Bid Buttons */}
      {reel.isLive && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Gavel className="w-5 h-5" />
                Place Your Bid
              </div>
              <div className="flex items-center gap-2">
                <div className={cn(
                  "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                  isConnected
                    ? "bg-green-100 text-green-700"
                    : "bg-red-100 text-red-700"
                )}>
                  {isConnected ? (
                    <>
                      <Wifi className="w-3 h-3" />
                      Live
                    </>
                  ) : (
                    <>
                      <WifiOff className="w-3 h-3" />
                      Offline
                    </>
                  )}
                </div>
                {isConnected && timeSyncStatus && (
                  <div className={cn(
                    "flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium",
                    timeSyncStatus.synchronized
                      ? "bg-blue-100 text-blue-700"
                      : "bg-yellow-100 text-yellow-700"
                  )}>
                    <Timer className="w-3 h-3" />
                    {timeSyncStatus.synchronized ? "Synced" : "Local"}
                  </div>
                )}
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="text-sm text-gray-600 mb-2">
                Minimum bid: {formatCurrency(getMinimumBid())}
              </div>
              <div className="grid grid-cols-3 gap-2">
                <Button
                  variant="outline"
                  onClick={() => handleQuickBid(50)}
                  disabled={isPlacingBid || !isConnected}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  $50
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleQuickBid(100)}
                  disabled={isPlacingBid || !isConnected}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  $100
                </Button>
                <Button
                  variant="outline"
                  onClick={() => handleQuickBid(250)}
                  disabled={isPlacingBid || !isConnected}
                  className="flex items-center gap-1"
                >
                  <Plus className="w-4 h-4" />
                  $250
                </Button>
              </div>
            </div>

            <Separator />

            <div>
              <div className="text-sm text-gray-600 mb-2">Custom Amount</div>
              <div className="flex gap-2">
                <Input
                  type="number"
                  placeholder="Enter amount"
                  value={bidAmount}
                  onChange={(e) => setBidAmount(e.target.value)}
                  disabled={isPlacingBid || !isConnected}
                  min={getMinimumBid()}
                />
                <Button
                  onClick={handleCustomBid}
                  disabled={isPlacingBid || !bidAmount || !isConnected}
                  className="bg-green-600 hover:bg-green-700"
                >
                  {!isConnected ? "Offline" : isPlacingBid ? "Placing..." : "Bid"}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bid History */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Bids</CardTitle>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-64">
            {bids.length > 0 ? (
              <div className="space-y-3">
                {bids.map((bid, index) => (
                  <div
                    key={bid.id}
                    className="flex items-center justify-between p-3 rounded-lg bg-gray-50"
                  >
                    <div className="flex items-center gap-3">
                      <Avatar className="w-8 h-8">
                        <AvatarImage src={bid.bidder.avatar} />
                        <AvatarFallback>{bid.bidder.name[0]}</AvatarFallback>
                      </Avatar>
                      <div>
                        <div className="font-medium text-sm">
                          {bid.bidder.name}
                          {bid.bidder.flightNumber && (
                            <span className="ml-2">
                              <FlightNumberDisplay flightNumber={bid.bidder.flightNumber} />
                            </span>
                          )}
                        </div>
                        <div className="text-xs text-gray-500">
                          {bid.timestamp.toLocaleTimeString()}
                          {bid.serverTime && timeSyncStatus?.synchronized && (
                            <span className="ml-1 text-blue-500">•</span>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-green-600">
                        {formatCurrency(bid.amount)}
                      </div>
                      {index === 0 && (
                        <Badge variant="secondary" className="text-xs mt-1">
                          Leading
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 py-8">
                No bids yet. Be the first to bid!
              </div>
            )}
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
}
